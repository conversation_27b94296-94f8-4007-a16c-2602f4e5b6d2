<script setup lang="ts">
import { onLoad, onUnload } from '@dcloudio/uni-app'
import { computed, ref } from 'vue'

// 类型定义
interface ImageItem {
  url: string
  description?: string
  time?: string
  iconImg?: string
  isQualified?: boolean
}

interface SwiperChangeEvent {
  detail: {
    current: number
  }
}

// 状态管理
const images = ref<ImageItem[]>([])
const currentIndex = ref(0)
const isLoading = ref(true)
const hasError = ref(false)
const errorMessage = ref('')
const isNavHidden = ref(false)
const imageLoadingStates = ref<Record<number, boolean>>({})
const imageErrorStates = ref<Record<number, boolean>>({})

// 当前显示的图片信息
const currentImage = computed(() => {
  if (images.value.length === 0) {
    return { url: '', description: '', time: '', iconImg: '', isQualified: true }
  }
  return images.value[currentIndex.value]
})

// 初始化数据
onLoad(async () => {
  try {
    isLoading.value = true
    hasError.value = false

    // 从缓存中获取图片数据
    const imageData = uni.getStorageSync('previewImageData')
    const initialIndex = uni.getStorageSync('previewCurrentIndex') || 0

    if (imageData && Array.isArray(imageData) && imageData.length > 0) {
      images.value = imageData
      currentIndex.value = Math.max(0, Math.min(initialIndex, imageData.length - 1))

      // 初始化图片加载状态
      imageData.forEach((_, index) => {
        imageLoadingStates.value[index] = true
        imageErrorStates.value[index] = false
      })

      isLoading.value = false
    }
    else {
      throw new Error('图片数据为空或格式错误')
    }
  }
  catch (error) {
    console.error('初始化预览页面失败:', error)
    hasError.value = true
    isLoading.value = false
    errorMessage.value = error instanceof Error ? error.message : '获取图片数据失败'
  }
})

// 清理缓存
onUnload(() => {
  try {
    uni.removeStorageSync('previewImageData')
    uni.removeStorageSync('previewCurrentIndex')
  }
  catch (error) {
    console.warn('清理缓存失败:', error)
  }
})

// 处理轮播图变化
function handleSwiperChange(e: SwiperChangeEvent) {
  currentIndex.value = e.detail.current
}

// 返回上一页
function handleBack() {
  uni.navigateBack({
    fail: () => {
      // 如果返回失败，尝试跳转到首页
      uni.reLaunch({
        url: '/pages/home/<USER>',
      })
    },
  })
}

// 重试加载
function handleRetry() {
  onLoad()
}

// 切换导航栏显示状态
function toggleNavBar() {
  isNavHidden.value = !isNavHidden.value
}

// 处理图片加载完成
function handleImageLoad(index: number) {
  imageLoadingStates.value[index] = false
  imageErrorStates.value[index] = false
}

// 处理图片加载失败
function handleImageError(index: number) {
  imageLoadingStates.value[index] = false
  imageErrorStates.value[index] = true
}

// 重试加载单张图片
function retryLoadImage(index: number) {
  imageLoadingStates.value[index] = true
  imageErrorStates.value[index] = false

  // 触发图片重新加载（通过修改src实现）
  const image = images.value[index]
  if (image) {
    const originalUrl = image.url
    image.url = ''
    setTimeout(() => {
      image.url = originalUrl
    }, 100)
  }
}

// 显示图片操作菜单
function showImageActions(url: string) {
  uni.showActionSheet({
    itemList: ['保存图片', '分享图片'],
    success: (res) => {
      if (res.tapIndex === 0) {
        saveImage(url)
      }
      else if (res.tapIndex === 1) {
        shareImage(url)
      }
    },
  })
}

// 下载当前图片
function handleDownload() {
  if (currentImage.value.url) {
    saveImage(currentImage.value.url)
  }
}

// 保存图片到相册
async function saveImage(url: string) {
  try {
    uni.showLoading({
      title: '保存中...',
      mask: true,
    })

    const downloadResult = await new Promise<UniApp.DownloadFileSuccessCallbackResult>((resolve, reject) => {
      uni.downloadFile({
        url,
        success: resolve,
        fail: reject,
      })
    })

    if (downloadResult.statusCode === 200) {
      await new Promise<void>((resolve, reject) => {
        uni.saveImageToPhotosAlbum({
          filePath: downloadResult.tempFilePath,
          success: () => resolve(),
          fail: reject,
        })
      })

      uni.hideLoading()
      uni.showToast({
        title: '保存成功',
        icon: 'success',
      })
    }
    else {
      throw new Error('下载失败')
    }
  }
  catch (error) {
    uni.hideLoading()
    console.error('保存图片失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'none',
    })
  }
}

// 分享图片
function shareImage(url: string) {
  // 这里可以根据需要实现分享功能
  uni.showToast({
    title: '分享功能待实现',
    icon: 'none',
  })
}
</script>

<template>
  <view class="image-preview-container">
    <!-- 加载状态 -->
    <view v-if="isLoading" class="loading-overlay">
      <view class="loading-content">
        <view class="loading-spinner" />
        <text class="loading-text">
          加载中...
        </text>
      </view>
    </view>

    <!-- 错误状态 -->
    <view v-else-if="hasError" class="error-overlay">
      <view class="error-content">
        <uni-icons type="info" size="48" color="#ff4444" />
        <text class="error-text">
          {{ errorMessage }}
        </text>
        <view class="error-actions">
          <view class="retry-button" @tap="handleRetry">
            <text>重试</text>
          </view>
          <view class="back-button-error" @tap="handleBack">
            <text>返回</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 正常预览状态 -->
    <template v-else>
      <!-- 顶部导航栏 -->
      <view class="nav-bar" :class="{ 'nav-bar-hidden': isNavHidden }">
        <view class="back-button" @tap="handleBack">
          <uni-icons type="closeempty" size="24" color="#fff" />
        </view>
        <view class="counter">
          <text>{{ currentIndex + 1 }}/{{ images.length }}</text>
        </view>
        <view class="nav-actions">
          <view class="action-button" @tap="handleDownload">
            <uni-icons type="download" size="20" color="#fff" />
          </view>
        </view>
      </view>

      <!-- 图片轮播区域 -->
      <swiper
        class="swiper-container"
        :current="currentIndex"
        :indicator-dots="false"
        :autoplay="false"
        :circular="images.length > 1"
        @change="handleSwiperChange"
        @tap="toggleNavBar"
      >
        <swiper-item v-for="(item, index) in images" :key="`${index}-${item.url}`" class="swiper-item">
          <view class="image-container">
            <image
              :src="item.url"
              mode="aspectFit"
              class="preview-image"
              :class="{ 'image-loading': imageLoadingStates[index] }"
              @load="handleImageLoad(index)"
              @error="handleImageError(index)"
              @longpress="showImageActions(item.url)"
              @tap.stop="toggleNavBar"
            />

            <!-- 图片加载状态 -->
            <view v-if="imageLoadingStates[index]" class="image-loading-overlay">
              <view class="image-loading-spinner" />
            </view>

            <!-- 图片加载失败状态 -->
            <view v-if="imageErrorStates[index]" class="image-error-overlay" @tap="retryLoadImage(index)">
              <uni-icons type="refresh" size="32" color="#fff" />
              <text class="image-error-text">
                点击重试
              </text>
            </view>

            <!-- 状态图标 -->
            <image
              v-if="item.iconImg && !imageErrorStates[index]"
              :src="item.iconImg"
              class="status-icon"
            />
          </view>
        </swiper-item>
      </swiper>

      <!-- 底部信息栏 -->
      <view class="info-bar" :class="{ 'info-bar-hidden': isNavHidden }">
        <view v-if="currentImage.description" class="description">
          <text>{{ currentImage.description }}</text>
        </view>
        <view v-if="currentImage.time" class="time">
          <text>{{ currentImage.time }}</text>
        </view>
      </view>
    </template>
  </view>
</template>

<style lang="scss" scoped>
.image-preview-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #000;
  display: flex;
  flex-direction: column;
  z-index: 999;
}

.nav-bar {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.5);
  position: relative;
  z-index: 10;
}

.back-button {
  font-size: 18px;
  padding: 5px;
}

.counter {
  font-size: 14px;
  margin-left: -20rpx;
}

.swiper-container {
  flex: 1;
  width: 100%;
}

.swiper-item {
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-image {
  width: 100%;
  height: 100%;
}

// 加载状态样式
.loading-overlay,
.error-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #000;
  z-index: 1000;
}

.loading-content,
.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: #fff;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text,
.error-text {
  font-size: 16px;
  color: #fff;
}

.error-actions {
  display: flex;
  gap: 12px;
  margin-top: 8px;
}

.retry-button,
.back-button-error {
  padding: 8px 16px;
  border: 1px solid #fff;
  background: transparent;
  color: #fff;
  border-radius: 4px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:active {
    background-color: rgba(255, 255, 255, 0.1);
  }

  text {
    color: #fff;
    font-size: 14px;
  }
}

// 导航栏隐藏动画
.nav-bar {
  transition:
    opacity 0.3s ease,
    transform 0.3s ease;

  &.nav-bar-hidden {
    opacity: 0;
    transform: translateY(-100%);
    pointer-events: none;
  }
}

.back-button {
  &:active {
    opacity: 0.7;
  }
}

.counter {
  font-weight: 500;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.action-button {
  padding: 5px;

  &:active {
    opacity: 0.7;
  }
}

// 图片容器优化
.image-container {
  position: relative;
}

.preview-image {
  transition: opacity 0.3s ease;

  &.image-loading {
    opacity: 0.5;
  }
}

// 图片加载状态
.image-loading-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 5;
}

.image-loading-spinner {
  width: 32px;
  height: 32px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

// 图片错误状态
.image-error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.8);
  color: #fff;
  z-index: 5;
}

.image-error-text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

// 状态图标
.status-icon {
  position: absolute;
  top: 33%;
  right: 16px;
  width: 64px;
  height: 64px;
  filter: drop-shadow(0 0 4px rgba(0, 0, 0, 0.5));
  z-index: 10;
}

// 底部信息栏
.info-bar {
  padding: 12px 15px;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  transition:
    opacity 0.3s ease,
    transform 0.3s ease;

  &.info-bar-hidden {
    opacity: 0;
    transform: translateY(100%);
    pointer-events: none;
  }
}

.description {
  font-size: 14px;
  margin-bottom: 4px;
  line-height: 1.4;
}

.time {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

// 响应式适配
@media (max-width: 768px) {
  .nav-bar {
    height: 40px;
    padding: 0 12px;
  }

  .counter {
    font-size: 13px;
  }

  .status-icon {
    width: 48px;
    height: 48px;
    right: 12px;
  }

  .info-bar {
    padding: 10px 12px;
  }

  .description {
    font-size: 13px;
  }

  .time {
    font-size: 11px;
  }
}
</style>
