<template>
  <view class="example-container">
    <view class="title">
      <text>图片预览组件使用示例</text>
    </view>

    <view class="image-grid">
      <view
        v-for="(image, index) in sampleImages"
        :key="index"
        class="image-item"
        @tap="previewImages(index)"
      >
        <image :src="image.url" mode="aspectFill" class="thumbnail"></image>
        <view class="image-info">
          <text class="image-desc">{{ image.description }}</text>
          <text class="image-time">{{ image.time }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 示例图片数据
const sampleImages = ref([
  {
    url: 'https://via.placeholder.com/400x300/FF6B6B/FFFFFF?text=Image+1',
    description: '这是第一张示例图片',
    time: '2024-01-15 10:30:00',
    iconImg: 'https://via.placeholder.com/64x64/4ECDC4/FFFFFF?text=✓',
    isQualified: true
  },
  {
    url: 'https://via.placeholder.com/400x300/4ECDC4/FFFFFF?text=Image+2',
    description: '这是第二张示例图片，描述可能会比较长一些，用来测试文本换行效果',
    time: '2024-01-15 11:45:00',
    iconImg: 'https://via.placeholder.com/64x64/FF6B6B/FFFFFF?text=✗',
    isQualified: false
  },
  {
    url: 'https://via.placeholder.com/400x300/45B7D1/FFFFFF?text=Image+3',
    description: '第三张图片',
    time: '2024-01-15 14:20:00',
    iconImg: 'https://via.placeholder.com/64x64/4ECDC4/FFFFFF?text=✓',
    isQualified: true
  }
])

// 预览图片
const previewImages = (startIndex: number) => {
  try {
    // 将图片数据保存到缓存
    uni.setStorageSync('previewImageData', sampleImages.value)
    uni.setStorageSync('previewCurrentIndex', startIndex)
    
    // 跳转到图片预览页面
    uni.navigateTo({
      url: '/pages/image-preview/index',
      success: () => {
        console.log('跳转到图片预览页面成功')
      },
      fail: (err) => {
        console.error('跳转失败:', err)
        uni.showToast({
          title: '跳转失败',
          icon: 'none'
        })
      }
    })
  } catch (error) {
    console.error('预览图片失败:', error)
    uni.showToast({
      title: '预览失败',
      icon: 'none'
    })
  }
}
</script>

<style lang="scss" scoped>
.example-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.image-item {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }
}

.thumbnail {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.image-info {
  padding: 12px;
}

.image-desc {
  display: block;
  font-size: 14px;
  color: #333;
  line-height: 1.4;
  margin-bottom: 8px;
}

.image-time {
  display: block;
  font-size: 12px;
  color: #666;
}

@media (max-width: 768px) {
  .example-container {
    padding: 16px;
  }
  
  .image-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .thumbnail {
    height: 160px;
  }
}
</style>
