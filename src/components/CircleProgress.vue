<script lang="ts" setup>
interface CircleProgressProps {
  /**
   * 模式：chart图表模式，timing定时器模式
   * @default 'chart'
   */
  mode?: 'chart' | 'timing'
  /**
   * 进度值 (0-100)，chart模式使用
   */
  value?: number
  /**
   * 激活状态颜色
   * @default '#00996B'
   */
  activeColor?: string
  /**
   * 默认背景颜色
   * @default '#E5E6EB'
   */
  defaultColor?: string
  /**
   * 中心区域背景色
   * @default '#FFFFFF'
   */
  centerBgColor?: string
  /**
   * 圆环半径（包含圆环宽度）
   * @default 32
   */
  radius?: number
  /**
   * 圆环宽度
   * @default 5
   */
  barWidth?: number
  /**
   * 开始角度
   * @default 0
   */
  startDeg?: number
  /**
   * 定时器持续时间（秒），timing模式使用
   * @default 1
   */
  duration?: number
}

const props = withDefaults(defineProps<CircleProgressProps>(), {
  mode: 'chart',
  value: 0,
  activeColor: '#00996B',
  defaultColor: '#E5E6EB',
  centerBgColor: '#FFFFFF',
  radius: 32,
  barWidth: 5,
  startDeg: 0,
  duration: 1,
})

// 定时器状态
const isStart = ref(false)
const showViewToken = ref(Date.now())

// 容器样式
const containerStyles = computed(() => ({
  borderRadius: `${props.radius}px`,
  height: `${props.radius * 2}px`,
  width: `${props.radius * 2}px`,
  transform: `rotate(${props.startDeg}deg)`,
  backgroundColor: props.activeColor
}))

// 左半圆样式
const leftStyles = computed(() => ({
  height: `${props.radius * 2}px`,
  width: `${props.radius}px`,
  backgroundColor: props.defaultColor,
  borderTopLeftRadius: `${props.radius}px`,
  borderBottomLeftRadius: `${props.radius}px`,
  ...(props.mode === 'timing' ? {
    transitionDuration: `${isStart.value ? props.duration : 0}s`,
    transform: `rotate(${isStart.value ? 180 : -180}deg)`
  } : {
    transform: `rotate(${-180 + (props.value || 0) * 3.6}deg)`
  })
}))

// 右半圆样式
const rightStyles = computed(() => ({
  height: `${props.radius * 2}px`,
  width: `${props.radius}px`,
  backgroundColor: props.defaultColor,
  borderTopRightRadius: `${props.radius}px`,
  borderBottomRightRadius: `${props.radius}px`,
  ...(props.mode === 'timing' ? {
    backgroundColor: isStart.value ? props.activeColor : props.defaultColor,
    transitionDelay: `${isStart.value ? props.duration / 2 : 0}s`,
    transform: `rotate(${isStart.value ? 0 : -180}deg)`
  } : {
    backgroundColor: (props.value || 0) >= 50 ? props.activeColor : props.defaultColor,
    transform: `rotate(${(props.value || 0) >= 50 ? 0 : -180}deg)`,
  })
}))

// 中心区域样式
const centerStyles = computed(() => ({
  borderRadius: `${props.radius - props.barWidth}px`,
  height: `${(props.radius - props.barWidth) * 2}px`,
  width: `${(props.radius - props.barWidth) * 2}px`,
  transform: `translate(-50%, -50%) rotate(-${props.startDeg}deg)`,
  backgroundColor: props.centerBgColor,
  left: `${props.radius}px`,
  top: `${props.radius}px`
}))

// 定义事件
const emit = defineEmits<{
  timingEnd: []
}>()

// 开始定时器
const start = () => {
  if (props.mode === 'chart') return
  isStart.value = true
}

// 结束定时器
const end = () => {
  if (props.mode === 'chart') return
  isStart.value = false
}

// 定时器结束回调
const onTimingEnd = () => {
  emit('timingEnd')
}

// 暴露方法给父组件
defineExpose({
  start,
  end
})
</script>

<template>
  <div 
    class="circle-progress relative inline-flex items-center justify-center"
    :style="{ width: `${size}px`, height: `${size}px` }"
  >
    <svg 
      :width="size" 
      :height="size" 
      class="transform -rotate-90"
    >
      <!-- 背景圆环 -->
      <circle
        :cx="center"
        :cy="center"
        :r="radius"
        :stroke="backgroundColor"
        :stroke-width="strokeWidth"
        fill="none"
      />
      <!-- 进度圆环 -->
      <circle
        :cx="center"
        :cy="center"
        :r="radius"
        :stroke="activeColor"
        :stroke-width="strokeWidth"
        :stroke-dasharray="circumference"
        :stroke-dashoffset="animatedStrokeDashoffset"
        stroke-linecap="round"
        fill="none"
        class="transition-all duration-300 ease-out"
      />
    </svg>
    
    <!-- 中心内容 -->
    <div 
      v-if="showText || $slots.default" 
      class="absolute inset-0 flex items-center justify-center"
    >
      <slot>
        <span class="text-sm font-medium text-gray-700">
          {{ Math.round(animatedValue) }}%
        </span>
      </slot>
    </div>
  </div>
</template>

<style lang="less" scoped>
.circle-progress {
  svg {
    overflow: visible;
  }
}
</style>
